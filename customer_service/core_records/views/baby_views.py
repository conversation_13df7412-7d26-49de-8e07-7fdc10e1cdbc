from rest_framework.views import APIView

from core.authorization import Care<PERSON><PERSON><PERSON><PERSON><PERSON>ication, StaffWithSpecificPermissionOnly, MaternityOrStaffWithPermission
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.enums.baby import FeedingMethodChoice
from customer_service.core_records.models import (
    Newborn,
    NewbornCheckInAssessment,
    MaternityAdmission
)
from customer_service.core_records.models.baby import NewbornCareOneRecord, NewbornCareOperationRecord, \
    NewbornCareTwoRecord, NewbornDailyRequiredRecord, NewbornFeedingRecord, NewbornMonthAssessment
from customer_service.core_records.serializers import (
    NewbornCreateSerializer,
)
from customer_service.core_records.serializers.baby import NewbornCheckInAssessmentCreateSerializer, \
    NewbornCheckInAssessmentDetailSerializer, NewbornCheckInAssessmentUpdateSerializer, NewbornCareOneRecordCreateSerializer, \
    NewbornCareOneRecordDetailSerializer, NewbornCareOneRecordList<PERSON>erializer, NewbornCareOneRecordUpdateSerializer, \
    NewbornCareOperationRecordCreateSerializer, NewbornCareOperationRecordDetailSerializer, \
    NewbornCareOperationRecordListSerializer, NewbornCareOperationRecordUpdateSerializer, \
    NewbornCareTwoRecordCreateSerializer, NewbornCareTwoRecordDetailSerializer, NewbornCareTwoRecordListSerializer, \
    NewbornCareTwoRecordUpdateSerializer, NewbornDailyRequiredRecordCreateSerializer, NewbornDailyRequiredRecordListSerializer, NewbornDailyRequiredRecordUpdateSerializer, NewbornDetailSerializer, NewbornFeedingRecordCreateSerializer, \
    NewbornFeedingRecordDetailSerializer, NewbornFeedingRecordListSerializer, NewbornFeedingRecordUpdateSerializer, NewbornMonthAssessmentCreateSerializer, NewbornMonthAssessmentDetailSerializer, NewbornMonthAssessmentUpdateSerializer, NewbornDailyRequiredRecordDetailSerializer  
from permissions.enum import PermissionEnum
from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly, MaternityOrStaffWithPermission
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.core_records.enums.baby import FeedingMethodChoice
from customer_service.core_records.models import (
    Newborn,
    NewbornCheckInAssessment,
    MaternityAdmission
)
from customer_service.core_records.models.baby import NewbornCareOneRecord, NewbornCareOperationRecord, \
    NewbornCareTwoRecord, NewbornDailyRequiredRecord, NewbornFeedingRecord, NewbornMonthAssessment
from customer_service.core_records.serializers import (
    NewbornCreateSerializer,
)
from customer_service.core_records.serializers.baby import NewbornCheckInAssessmentCreateSerializer, \
    NewbornCheckInAssessmentDetailSerializer, NewbornCheckInAssessmentUpdateSerializer, \
    NewbornCareOneRecordCreateSerializer, \
    NewbornCareOneRecordDetailSerializer, NewbornCareOneRecordListSerializer, NewbornCareOneRecordUpdateSerializer, \
    NewbornCareOperationRecordCreateSerializer, NewbornCareOperationRecordDetailSerializer, \
    NewbornCareOperationRecordListSerializer, NewbornCareOperationRecordUpdateSerializer, \
    NewbornCareTwoRecordCreateSerializer, NewbornCareTwoRecordDetailSerializer, NewbornCareTwoRecordListSerializer, \
    NewbornCareTwoRecordUpdateSerializer, NewbornDailyRequiredRecordCreateSerializer, \
    NewbornDailyRequiredRecordListSerializer, NewbornDailyRequiredRecordUpdateSerializer, NewbornDetailSerializer, \
    NewbornFeedingRecordCreateSerializer, \
    NewbornFeedingRecordDetailSerializer, NewbornFeedingRecordListSerializer, NewbornFeedingRecordUpdateSerializer, \
    NewbornMonthAssessmentCreateSerializer, NewbornMonthAssessmentDetailSerializer, \
    NewbornMonthAssessmentUpdateSerializer, NewbornDailyRequiredRecordDetailSerializer
from permissions.enum import PermissionEnum


class NewbornBaseView(APIView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    

# 新生儿记录列表基类
class NewbornRecordListBaseView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW

    model_class = None  
    serializer_class = None  
    success_message = "" 

    def get_base_queryset(self, nid):
 
        if not self.model_class:
            raise NotImplementedError("数据不存在")

        return self.model_class.objects.filter(
            newborn__nid=nid,
            newborn__maternity_admission__maternity_center=self.request.user.maternity_center
        )

    def apply_additional_filters(self, queryset):
        return queryset

    def get_ordering(self):
        return ['-created_at']

    def paginate_queryset(self, queryset):
        try:
            page = int(self.request.query_params.get('page', 1))
            page_size = int(self.request.query_params.get('page_size', 10))
        except ValueError:
            page = 1
            page_size = 10

        page = max(1, page)
        page_size = min(max(1, page_size), 100) 

        total_count = queryset.count()
        total_page = (total_count + page_size - 1) // page_size
        start = (page - 1) * page_size
        end = start + page_size

        paginated_data = queryset[start:end]

        return {
            'data': paginated_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_page': total_page
            }
        }

    def get(self, request, nid):

        try:
            queryset = self.get_base_queryset(nid)

            queryset = self.apply_additional_filters(queryset)

            ordering = self.get_ordering()
            if ordering:
                queryset = queryset.order_by(*ordering)

            paginated_result = self.paginate_queryset(queryset)

            if not self.serializer_class:
                raise NotImplementedError("数据类型不存在")

            serializer = self.serializer_class(paginated_result['data'], many=True)

            result = {
                'list': serializer.data,
                **paginated_result['pagination']
            }

            return make_response(
                code=0,
                msg=self.success_message or "获取列表成功",
                data=result
            )

        except Exception as e:
            return make_response(code=-1, msg=f"获取列表失败: {str(e)}")



# 新生儿每日必填记录列表基类
class NewbornDailyRequiredRecordListBaseView(NewbornRecordListBaseView):
    model_class = NewbornDailyRequiredRecord
    serializer_class = NewbornDailyRequiredRecordListSerializer
    success_message = "获取新生儿每日必填记录列表成功"

    def get_ordering(self):
        return ['-record_date']
    

# 获取新生儿每日必填记录详情
class NewbornDailyRequiredRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, record_id):
        try:
            instance = NewbornDailyRequiredRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornDailyRequiredRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = NewbornDailyRequiredRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取新生儿每日必填记录详情成功", data=serializer.data)
    
    

# 创建新生儿每日必填记录
class NewbornDailyRequiredRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        data = request.data.copy()
        try:
            newborn = Newborn.objects.get(nid=nid,maternity_admission__maternity_center=request.user.maternity_center)
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿不存在")
        
        record_date = data.get('record_date')
        
        if record_date:
            if NewbornDailyRequiredRecord.check_record_date_exists(nid, record_date):
                return make_response(code=-1, msg=f"{newborn.name}（{record_date}）已有每日必填记录，请选择其他日期")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn.id
        
        serializer = NewbornDailyRequiredRecordCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿每日必填记录成功", data=NewbornDailyRequiredRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建新生儿每日必填记录失败", data=serializer.errors)


# 更新新生儿每日必填记录
class NewbornDailyRequiredRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, record_id):
        
        data = request.data.copy()
        
        try:
            instance = NewbornDailyRequiredRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornDailyRequiredRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = data.get('record_date')
        
        if record_date:
            if NewbornDailyRequiredRecord.check_record_date_exists(instance.newborn.nid, record_date, instance.id):
                return make_response(code=-1, msg=f"{instance.newborn.name}（{record_date}）已有每日必填记录，请选择其他日期")
        
        serializer = NewbornDailyRequiredRecordUpdateSerializer(instance, data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新新生儿每日必填记录成功", data=NewbornDailyRequiredRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新新生儿每日必填记录失败", data=serializer.errors)
        

# 删除新生儿每日必填记录
class NewbornDailyRequiredRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def delete(self, request, record_id):
        
        try:
            instance = NewbornDailyRequiredRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
            instance.delete()
            return make_response(code=0, msg="删除新生儿每日必填记录成功")
        except NewbornDailyRequiredRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")

        



class NewbornListView(PaginationListBaseView):
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    serializer_class = NewbornDetailSerializer
    response_msg = "获取新生儿列表成功"
    error_response_msg = ""
    search_fields = ["name", "maternity_admission__maternity__name"]
    
    def get_queryset(self):

        aid = self.request.query_params.get('aid', None)
        
        base_queryset = Newborn.objects.filter(
            maternity_admission__maternity_center=self.request.user.maternity_center,
        )

        if aid:
            base_queryset = base_queryset.filter(maternity_admission__aid=aid)
                
        return base_queryset.order_by('-created_at')

    
# 创建新生儿记录
class NewbornCreateView(NewbornBaseView):
    
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request,aid):
        
        admission = MaternityAdmission.get_maternity_admission_by_aid(aid,request.user.maternity_center)
        if not admission:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        data = request.data.copy()
        data['maternity_admission'] = admission.id
        
        serializer = NewbornCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿记录成功", data=NewbornDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)
    
    
# 获取新生儿详情
class NewbornDetailView(NewbornBaseView):
    
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, nid):
        try:
            instance = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = NewbornDetailSerializer(instance)
        return make_response(code=0, msg="获取新生儿详情成功", data=serializer.data)

# 更新新生儿记录
class NewbornUpdateView(NewbornBaseView):
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, nid):
        if request.auth.get('user_type') == 'maternity':
            return make_response(code=-1, msg="非员工无法更新新生儿记录")

        try:
            instance = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center # 添加产妇中心过滤
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="记录不存在或无权访问")
            
        serializer = NewbornDetailSerializer(instance, data=request.data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新新生儿记录成功", data=serializer.data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)


# 删除新生儿记录
class NewbornDeleteView(NewbornBaseView):
    
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, nid):
        
        try:
            instance = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
            instance.delete()
            return make_response(code=0, msg='删除新生儿记录成功')
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg='记录不存在')



# 获取新生儿入住评估详情
class NewbornCheckInAssessmentDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, nid):
        
        try:
            newborn = Newborn.objects.get(nid=nid,maternity_admission__maternity_center=request.user.maternity_center)
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")
        
        try:
            nas = NewbornCheckInAssessment.objects.get(newborn=newborn)
        except NewbornCheckInAssessment.DoesNotExist:
            return make_response(code=0, msg="新生儿暂无入住评估记录")
        
        serializer = NewbornCheckInAssessmentDetailSerializer(nas)
        return make_response(code=0, msg="获取新生儿入住评估详情成功", data=serializer.data)

# 创建新生儿评估记录
class NewbornCheckInAssessmentCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        data = request.data.copy()
        
        
        try:
            newborn_instance = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")

        if NewbornCheckInAssessment.objects.filter(newborn=newborn_instance).exists():
            return make_response(code=-1, msg="当前新生儿已有评估记录，不允许创建新的记录")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn_instance.id
        
        serializer = NewbornCheckInAssessmentCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿入住评估记录成功", data=NewbornCheckInAssessmentDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)

# 更新新生儿评估记录
class NewbornCheckInAssessmentUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, nid):
        
        data = request.data.copy()

        try:
            nas = NewbornCheckInAssessment.objects.get(newborn__nid=nid,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCheckInAssessment.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        serializer = NewbornCheckInAssessmentUpdateSerializer(nas, data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新新生儿入住评估记录成功", data=NewbornCheckInAssessmentDetailSerializer(nas).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)

# 删除新生儿入住评估记录
class NewbornCheckInAssessmentDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, nid):
        
        try:
            nas = NewbornCheckInAssessment.objects.get(newborn__nid=nid,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCheckInAssessment.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        nas.delete()
        return make_response(code=0, msg="删除新生儿入住评估记录成功")
        


# 新生儿护理记录单（1）接口
# 获取新生儿护理记录单（1）列表
class NewbornCareOneRecordListView(NewbornRecordListBaseView):
    model_class = NewbornCareOneRecord
    serializer_class = NewbornCareOneRecordListSerializer
    success_message = "获取新生儿护理记录单（1）列表成功"
    
    def get_ordering(self):
        return ['-record_date']

# 获取新生儿护理记录单（1）详情
class NewbornCareOneRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, record_id):
        try:
            instance = NewbornCareOneRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareOneRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = NewbornCareOneRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取新生儿护理记录单（1）详情成功", data=serializer.data)
    

# 创建新生儿护理记录单（1）
class NewbornCareOneRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        try:
            newborn = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")
        
        data = request.data.copy()
        
        record_date = data.get('record_date')
        
        if not record_date:
            return make_response(code=-1, msg="日期不能为空")
        
        if  NewbornCareOneRecord.objects.filter(newborn=newborn, record_date=record_date).exists():
            return make_response(code=-1, msg=f"{newborn.name}（{record_date}）已有护理记录单（1），请勿重复创建")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn.id
        serializer = NewbornCareOneRecordCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿护理记录单（1）成功", data=NewbornCareOneRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)
    

# 更新新生儿护理记录单（1）
class NewbornCareOneRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, record_id):
        
        try:
            instance = NewbornCareOneRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareOneRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = request.data.get('record_date')
        
        if record_date:
            if NewbornCareOneRecord.check_record_date_exists(instance.newborn.nid, record_date, instance.newborn.maternity_admission.maternity_center, instance.id):
                return make_response(code=-1, msg=f"{instance.newborn.name}（{record_date}）已有护理记录单（1），请选择其他日期")
        
        serializer = NewbornCareOneRecordUpdateSerializer(instance, data=request.data)
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg="更新新生儿护理记录单（1）成功", data=NewbornCareOneRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)


# 删除新生儿护理记录单（1）
class NewbornCareOneRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):

        try:
            instance = NewbornCareOneRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareOneRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.newborn.name}（{instance.record_date}）的护理记录单（1）成功")



# 新生儿护理记录单（2）接口
# 获取新生儿护理记录单（2）列表
class NewbornCareTwoRecordListView(NewbornRecordListBaseView):
    model_class = NewbornCareTwoRecord
    serializer_class = NewbornCareTwoRecordListSerializer
    success_message = "获取新生儿护理记录单（2）列表成功"
    
    def get_ordering(self):
        return ['-record_date']
    

# 获取新生儿护理记录单（2）详情
class NewbornCareTwoRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, record_id):
        try:
            instance = NewbornCareTwoRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareTwoRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = NewbornCareTwoRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取新生儿护理记录单（2）详情成功", data=serializer.data)
    

# 创建新生儿护理记录单（2）
class NewbornCareTwoRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        try:
            newborn = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")
        
        data = request.data.copy()
        
        record_date = data.get('record_date')
        
        if not record_date:
            return make_response(code=-1, msg="日期不能为空")
        
        if  NewbornCareTwoRecord.objects.filter(newborn=newborn, record_date=record_date).exists():
            return make_response(code=-1, msg=f"{newborn.name}（{record_date}）已有护理记录单（2），请勿重复创建")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn.id
        serializer = NewbornCareTwoRecordCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿护理记录单（2）成功", data=NewbornCareTwoRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)
    
    

# 更新新生儿护理记录单（2）
class NewbornCareTwoRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, record_id):
        
        try:
            instance = NewbornCareTwoRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareTwoRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = request.data.get('record_date')
        
        if record_date:
            if NewbornCareTwoRecord.check_record_date_exists(instance.newborn.nid, record_date, instance.newborn.maternity_admission.maternity_center, instance.id):
                return make_response(code=-1, msg=f"{instance.newborn.name}（{record_date}）已有护理记录单（2），请选择其他日期")
        
        serializer = NewbornCareTwoRecordUpdateSerializer(instance, data=request.data)
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg="更新新生儿护理记录单（2）成功", data=NewbornCareTwoRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)
    
    
    
# 删除新生儿护理记录单（2）
class NewbornCareTwoRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):

        try:
            instance = NewbornCareTwoRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareTwoRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.newborn.name}（{instance.record_date}）的护理记录单（2）成功")   
    
    



# 新生儿喂养记录接口
# 获取新生儿喂养记录列表
class NewbornFeedingRecordListView(NewbornRecordListBaseView):
    
    model_class = NewbornFeedingRecord
    serializer_class = NewbornFeedingRecordListSerializer
    success_message = "获取新生儿喂养记录列表成功"
    
    def get_ordering(self):
        return ['-record_time']

# 获取新生儿喂养记录详情
class NewbornFeedingRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, record_id):
        try:
            instance = NewbornFeedingRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornFeedingRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = NewbornFeedingRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取新生儿喂养记录详情成功", data=serializer.data)
    

# 创建新生儿喂养记录
class NewbornFeedingRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        try:
            newborn = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")
        
        data = request.data.copy()
        
        record_time = data.get('record_time')
        feeding_method = data.get('feeding_method')
        
        if not feeding_method or feeding_method not in FeedingMethodChoice.values:
            return make_response(code=-1, msg="喂养方式错误")
        
        if not record_time:
            return make_response(code=-1, msg="喂养时间不能为空")
        
        if  NewbornFeedingRecord.objects.filter(newborn=newborn, record_time=record_time).exists():
            return make_response(code=-1, msg=f"{newborn.name}（{record_time}）已有喂养记录，请勿重复创建")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn.id
        serializer = NewbornFeedingRecordCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿喂养记录成功", data=NewbornFeedingRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)
    
    

# 更新新生儿喂养记录
class NewbornFeedingRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, record_id):
        
        try:
            instance = NewbornFeedingRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornFeedingRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_time = request.data.get('record_time')
        
        if record_time:
            if NewbornFeedingRecord.check_record_time_exists(instance.newborn.nid, record_time, instance.newborn.maternity_admission.maternity_center, instance.id):
                return make_response(code=-1, msg=f"{instance.newborn.name}（{record_time}）已有喂养记录，请选择其他日期")
        
        serializer = NewbornFeedingRecordUpdateSerializer(instance, data=request.data)
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg="更新新生儿喂养记录成功", data=NewbornFeedingRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)
    
    
    
# 删除新生儿喂养记录
class NewbornFeedingRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):

        try:
            instance = NewbornFeedingRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornFeedingRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.newborn.name}（{instance.record_date}）的喂养记录成功")   
    
    




# 新生儿护理操作记录接口
# 获取新生儿护理操作记录列表
class NewbornCareOperationRecordListView(NewbornRecordListBaseView):
    model_class = NewbornCareOperationRecord
    serializer_class = NewbornCareOperationRecordListSerializer
    success_message = "获取新生儿护理操作记录列表成功"
    
    def get_ordering(self):
        return ['-record_date']

# 获取新生儿护理操作记录详情
class NewbornCareOperationRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, record_id):
        try:
            instance = NewbornCareOperationRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareOperationRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = NewbornCareOperationRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取新生儿护理操作记录详情成功", data=serializer.data)
    

# 创建新生儿护理操作记录
class NewbornCareOperationRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        try:
            newborn = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")
        
        data = request.data.copy()
        
        record_date = data.get('record_date')
        
        if not record_date:
            return make_response(code=-1, msg="日期不能为空")
        
        if  NewbornCareOperationRecord.objects.filter(newborn=newborn, record_date=record_date).exists():
            return make_response(code=-1, msg=f"{newborn.name}（{record_date}）已有护理操作记录，请勿重复创建")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn.id
        serializer = NewbornCareOperationRecordCreateSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿护理操作记录成功", data=NewbornCareOperationRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)
    
    

# 更新新生儿护理操作记录
class NewbornCareOperationRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, record_id):
        
        try:
            instance = NewbornCareOperationRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareOperationRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = request.data.get('record_date')
        
        if record_date:
            if NewbornCareOperationRecord.check_record_date_exists(instance.newborn.nid, record_date, instance.newborn.maternity_admission.maternity_center, instance.id):
                return make_response(code=-1, msg=f"{instance.newborn.name}（{record_date}）已有护理操作记录，请选择其他日期")
        
        serializer = NewbornCareOperationRecordUpdateSerializer(instance, data=request.data)
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg="更新新生儿护理操作记录成功", data=NewbornCareOperationRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)
    
    
    
# 删除新生儿护理操作记录
class NewbornCareOperationRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):

        try:
            instance = NewbornCareOperationRecord.objects.get(record_id=record_id,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornCareOperationRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.newborn.name}（{instance.record_date}）的护理操作记录成功")   
    
    


# 获取新生儿满月评估详情
class NewbornMonthAssessmentDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [MaternityOrStaffWithPermission]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, nid):
        
        try:
            newborn = Newborn.objects.get(nid=nid,maternity_admission__maternity_center=request.user.maternity_center)
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")
        
        try:
            nas = NewbornMonthAssessment.objects.get(newborn=newborn)
        except NewbornMonthAssessment.DoesNotExist:
            return make_response(code=0, msg="新生儿暂无满月评估记录")
        
        serializer = NewbornMonthAssessmentDetailSerializer(nas)
        return make_response(code=0, msg="获取新生儿满月评估详情成功", data=serializer.data)

# 创建新生儿满月评估
class NewbornMonthAssessmentCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def post(self, request, nid):
        
        data = request.data.copy()
        
        try:
            newborn_instance = Newborn.objects.get(
                nid=nid,
                maternity_admission__maternity_center=request.user.maternity_center
            )
        except Newborn.DoesNotExist:
            return make_response(code=-1, msg="新生儿记录不存在")

        if NewbornMonthAssessment.objects.filter(newborn=newborn_instance).exists():
            return make_response(code=-1, msg=f"当前{newborn_instance.name}已有满月评估记录，请勿重复创建")
        
        data['creator'] = request.user.id
        data['newborn'] = newborn_instance.id
        
        serializer = NewbornMonthAssessmentCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建新生儿满月评估记录成功", data=NewbornMonthAssessmentDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建失败", data=serializer.errors)


# 更新新生儿满月评估记录
class NewbornMonthAssessmentUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def put(self, request, nid):
        
        data = request.data.copy()

        try:
            nas = NewbornMonthAssessment.objects.get(newborn__nid=nid,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornMonthAssessment.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        serializer = NewbornMonthAssessmentUpdateSerializer(nas, data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新新生儿满月评估记录成功", data=NewbornMonthAssessmentDetailSerializer(nas).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)


# 删除新生儿满月评估记录
class NewbornMonthAssessmentDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, nid):

        try:
            instance = NewbornMonthAssessment.objects.get(newborn__nid=nid,newborn__maternity_admission__maternity_center=request.user.maternity_center)
        except NewbornMonthAssessment.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.newborn.name}（{instance.assessment_date}）的满月评估记录成功")   