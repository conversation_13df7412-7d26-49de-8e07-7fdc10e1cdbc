from django.db import models

from core.generate_hashid import generate_maternity_check_in_assessment_code, \
    generate_maternity_daily_physical_care_record_code, generate_maternity_daily_required_record_code, \
    generate_maternity_diet_record_code, generate_maternity_rehabilitation_assessment_record_code
from core.model import BaseModel, JSONListValidator, PositiveFloatField
from customer_service.core_records.enums.maternal import ActivityEnum, AppetiteEnum, BreastSituationEnum, \
    DefecationEnum, DietaryRequirementsEnum, EatingSituationEnum, EmotionEnum, ExerciseAfterEvaluationEnum, \
    FaceColorEnum, FamilyAttitudeToPatientEnum, IncisionAbnormalEnum, IncisionPositionEnum, IncisionSituationEnum, \
    KnowledgeOfRehabilitationEnum, MilkSituationEnum, NippleAbnormalEnum, NippleSituationEnum, OralMucosaEnum, \
    ParticipationInPregnancyEducationEnum, RehabilitationIncisionSituationEnum, SpecialDietFeaturesEnum, UrinationEnum
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from user.models import Staff


# 产妇每日必填
class MaternityDailyRequiredRecord(BaseModel):
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, related_name='maternity_daily_required')
    # 记录日期
    record_date = models.DateField(verbose_name='记录日期')
    # 血压
    blood_pressure = models.CharField(verbose_name='血压', max_length=30)
    # 体重
    weight = PositiveFloatField(verbose_name='体重')
    # 体温
    temperature = PositiveFloatField(verbose_name='体温')
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_maternity_daily_required_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_maternity_daily_required',verbose_name='创建人')
    
    
    class Meta:
        verbose_name = "产妇每日必填"
        verbose_name_plural = "产妇每日必填"
        # unique_together = [['maternity_admission', 'record_date']]
        
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} ({self.record_date}) 的每日必填"
    
    # 检查是否存在每日必填记录
    @classmethod
    def check_record_date_exists(cls,maternity_admission_id,record_date,exclude_id=None):
        if exclude_id:
            return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exclude(id=exclude_id).exists()
        return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exists()
    
    # 获取列表
    @classmethod
    def get_list(cls,maternity_admission_id):
        return cls.objects.filter(maternity_admission_id=maternity_admission_id).order_by('-record_date')
    


# 产妇入住评估单
class MaternityCheckInAssessment(BaseModel):
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, related_name='maternity_check_in_assessments')
    # 入所时间
    admission_time = models.DateTimeField(verbose_name='入所时间')
    #有无出院小结
    has_discharge_summary = models.BooleanField(verbose_name='有无出院小结', default=False,blank=True)
    # 现在是否用药
    is_now_medication = models.BooleanField(verbose_name='现在是否用药', default=False,blank=True)
    # 是否有过敏食物
    has_allergic_food = models.BooleanField(verbose_name='是否有过敏食物', default=False,blank=True)
    # 食物过敏反应
    food_allergic_reaction = models.TextField(verbose_name='食物过敏反应', blank=True,default="")
    #是否有过敏药物
    has_allergic_drug = models.BooleanField(verbose_name='是否有过敏药物', default=False,blank=True)
    # 药物过敏反应
    drug_allergic_reaction = models.TextField(verbose_name='药物过敏反应', blank=True,default="")
    # 出院诊断
    discharge_diagnosis = models.TextField(verbose_name='出院诊断', blank=True,default="")
    # 孕期并发症合并症
    pregnancy_complications_and_comorbidities = models.TextField(verbose_name='孕期并发症合并症', blank=True,default="")
    # 分娩特殊情况
    delivery_special_situation = models.TextField(verbose_name='分娩特殊情况', blank=True,default="")
    # 是否是急性疾病治疗期
    is_acute_disease_treatment_period = models.BooleanField(verbose_name='是否是急性疾病治疗期', default=False,blank=True)
    # 是否是传染病传染期
    is_infectious_disease_period = models.BooleanField(verbose_name='是否是传染病传染期', default=False,blank=True)
    # 有无精神疾病史
    has_psychiatric_disease_history = models.BooleanField(verbose_name='有无精神疾病史', default=False,blank=True)
    # 有无心理疾病
    has_psychological_disease = models.BooleanField(verbose_name='有无心理疾病', default=False,blank=True)
    # 情绪(多选)
    emotion = models.JSONField(verbose_name='情绪(多选)',blank=True,default=list,validators=[JSONListValidator(EmotionEnum.choices)])
    # 食欲
    appetite = models.CharField(verbose_name='食欲', max_length=30,blank=True,choices=AppetiteEnum.choices,default=AppetiteEnum.UNKNOWN)
    # 饮食要求
    dietary_requirements = models.CharField(verbose_name='饮食要求', max_length=30,blank=True,choices=DietaryRequirementsEnum.choices,default=DietaryRequirementsEnum.UNKNOWN)
    # 饮食要求其他描述
    dro_description = models.TextField(verbose_name='饮食要求其他描述', blank=True,default="")
    # 有无饮食嗜好
    has_food_preference = models.BooleanField(verbose_name='有无饮食嗜好', default=False,blank=True)
    # 饮食嗜好描述
    food_preference_description = models.TextField(verbose_name='饮食嗜好描述', blank=True,default="")
    # 过敏食物
    allergic_food = models.TextField(verbose_name='过敏食物', blank=True,default="")
    # 排尿
    urination = models.CharField(verbose_name='排尿', max_length=30,blank=True,choices=UrinationEnum.choices,default=UrinationEnum.UNKNOWN)
    # 排尿次数
    urination_times = models.PositiveIntegerField(verbose_name='排尿次数', blank=True,null=True)
    # 有无尿痛
    has_urination_pain = models.BooleanField(verbose_name='有无尿痛', default=False,blank=True)
    # 排便
    defecation  = models.CharField(verbose_name='排便', max_length=30,blank=True,choices=DefecationEnum.choices,default=DefecationEnum.UNKNOWN)
    # 排便次数
    defecation_times = models.PositiveIntegerField(verbose_name='排便次数', blank=True,null=True)
    # 对康复知识熟悉程度
    knowledge_of_rehabilitation = models.CharField(verbose_name='对康复知识熟悉程度', blank=True,max_length=30,choices=KnowledgeOfRehabilitationEnum.choices,default=KnowledgeOfRehabilitationEnum.UNKNOWN)
    # 参加孕期教育情况
    participation_in_pregnancy_education = models.CharField(verbose_name='参加孕期教育情况', blank=True,max_length=30,choices=ParticipationInPregnancyEducationEnum.choices,default=ParticipationInPregnancyEducationEnum.UNKNOWN)
    # 家属对产妇的态度
    family_attitude_to_patient = models.CharField(verbose_name='家属对产妇的态度', blank=True,max_length=30,choices=FamilyAttitudeToPatientEnum.choices,default=FamilyAttitudeToPatientEnum.UNKNOWN)
    # 家属对产妇的态度其他描述
    fatp_pther_description = models.TextField(verbose_name='家属对产妇的态度其他描述', blank=True,default="")
    # 体温
    temperature = PositiveFloatField(verbose_name='体温', blank=True,null=True)
    # 血压
    blood_pressure = models.CharField(verbose_name='血压', max_length=30,blank=True,default="")
    # 脉搏
    pulse = models.PositiveIntegerField(verbose_name='脉搏', blank=True,null=True)
    # 呼吸
    respiration = models.PositiveIntegerField(verbose_name='呼吸', blank=True,null=True)
    # 体重
    weight = PositiveFloatField(verbose_name='体重', blank=True,null=True)
    # 身高
    height = PositiveFloatField(verbose_name='身高', blank=True,null=True)
    # 面色
    face_color = models.CharField(verbose_name='面色', blank=True,max_length=30,choices=FaceColorEnum.choices,default=FaceColorEnum.UNKNOWN)
    # 口腔黏膜
    oral_mucosa = models.CharField(verbose_name='口腔黏膜', blank=True,max_length=30,choices=OralMucosaEnum.choices,default=OralMucosaEnum.UNKNOWN)
    # 活动
    activity = models.CharField(verbose_name='活动', blank=True,max_length=30,choices=ActivityEnum.choices,default=ActivityEnum.UNKNOWN)
    # 活动受限部位
    activity_limited_part = models.CharField(verbose_name='活动受限部位', max_length=100,blank=True,default="")
    # 恶露色
    lochia_color = models.CharField(verbose_name='恶露色',max_length=100,blank=True,default="")
    # 恶露量
    lochia_amount = models.CharField(verbose_name='恶露量',max_length=100,blank=True,default="")
    # 恶露嗅
    lochia_smell = models.CharField(verbose_name='恶露嗅',max_length=100,blank=True,default="")
    # 宫底高度
    uterus_height = models.CharField(verbose_name='宫底高度',max_length=100,blank=True,default="")
    # 切口位置
    incision_position = models.CharField(verbose_name='切口位置',blank=True,max_length=30,choices=IncisionPositionEnum.choices,default=IncisionPositionEnum.UNKNOWN)
    # 切口情况
    incision_situation = models.CharField(verbose_name='切口情况',blank=True,max_length=30,choices=IncisionSituationEnum.choices,default=IncisionSituationEnum.UNKNOWN)
    # 切口异常（多选）
    incision_abnormal = models.JSONField(verbose_name='切口异常（多选）',blank=True,default=list,validators=[JSONListValidator(IncisionAbnormalEnum.choices)])
    # 乳房情况（多选）
    breast_situation= models.JSONField(verbose_name='乳房（多选）',blank=True,default=list,validators=[JSONListValidator(BreastSituationEnum.choices)])
    # 乳头情况
    nipple_situation = models.CharField(verbose_name='乳头情况',blank=True,max_length=30,choices=NippleSituationEnum.choices,default=NippleSituationEnum.UNKNOWN)
    # 乳头异常（多选）
    nipple_abnormal = models.JSONField(verbose_name='乳头异常（多选）',blank=True,default=list,validators=[JSONListValidator(NippleAbnormalEnum.choices)])
    # 乳汁情况
    milk_situation = models.CharField(verbose_name='乳汁情况',blank=True,max_length=30,choices=MilkSituationEnum.choices,default=MilkSituationEnum.UNKNOWN)
    # 乳房其他情况
    breast_other_situation = models.CharField(verbose_name='乳房其他情况',blank=True,max_length=200,default="")
    # 特殊发现
    special_findings = models.TextField(verbose_name='特殊发现',blank=True,default="")
    # 既往孕产史
    previous_pregnancy_history = models.TextField(verbose_name='既往孕产史',blank=True,default="")
    # 产妇护理要点
    maternal_nursing_points = models.TextField(verbose_name='产妇护理要点',blank=True,default="")
    # 签名
    signature = models.CharField(verbose_name='签名',blank=True,max_length=30,default="")
    # 评估时间
    assessment_time = models.DateTimeField(verbose_name='评估时间',blank=True,null=True)
    # 编号
    assessment_id = models.CharField(max_length=50, verbose_name="评估编号",blank=True, default=generate_maternity_check_in_assessment_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_maternity_check_in_assessments',verbose_name='创建人',blank=True,null=True)
    

    def __str__(self):
        return f"{self.maternity_admission.maternity.name} ({self.assessment_time}) 的入住评估 "

    class Meta:
        verbose_name = "产妇入住评估单"
        verbose_name_plural = "产妇入住评估单"
    
    # 检查是否存在入住评估记录
    @classmethod
    def check_if_exists(cls,maternity_admission_id):
        if cls.objects.filter(maternity_admission_id=maternity_admission_id).exists():
            return True
        return False
    
    

# 产妇康复护理评估记录
class MaternityRehabilitationAssessmentRecord(BaseModel):
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, related_name='maternity_rehabilitation_assessments')
    # 记录日期
    record_date = models.DateField(verbose_name='记录日期')
    # 产后天数
    postpartum_days = models.PositiveIntegerField(verbose_name='产后天数')
    # 体温
    temperature = PositiveFloatField(verbose_name='体温', blank=True,null=True)
    # 脉搏
    pulse = models.PositiveIntegerField(verbose_name='脉搏', blank=True,null=True)
    # 体重
    weight = PositiveFloatField(verbose_name='体重', blank=True,null=True)
    # 宫底高
    uterus_height = models.CharField(verbose_name='宫底高',max_length=100,blank=True,default="")
    # 切口情况
    incision_situation = models.JSONField(verbose_name='切口情况',blank=True,validators=[JSONListValidator(RehabilitationIncisionSituationEnum.choices)],default=list)
    # 恶露量
    lochia_amount = models.CharField(verbose_name='恶露量',max_length=100,blank=True,default="")
    # 恶露色
    lochia_color = models.CharField(verbose_name='恶露色',max_length=100,blank=True,default="")
    # 恶露嗅
    lochia_smell = models.CharField(verbose_name='恶露嗅',max_length=100,blank=True,default="")
    # 乳汁量
    milk_amount = models.CharField(verbose_name='乳汁量',max_length=100,blank=True,default="")
    # 乳房胀痛
    breast_pain = models.CharField(verbose_name='乳房胀痛',max_length=100,blank=True,default="")
    # 乳头皲裂
    nipple_cracked = models.CharField(verbose_name='乳头皲裂',max_length=100,blank=True,default="")
    # 指导意见
    guidance_opinion = models.TextField(verbose_name='指导意见',blank=True,default="")
    # 签名
    signature = models.CharField(verbose_name='签名',blank=True,max_length=30,default="")
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_maternity_rehabilitation_assessment_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_maternity_rehabilitation_assessments',verbose_name='创建人',blank=True,null=True)
    
    class Meta:
        verbose_name = "产妇康复护理评估记录"
        verbose_name_plural = "产妇康复护理评估记录"
        unique_together = [['maternity_admission', 'record_date']]
        
    
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} ({self.record_date}) 的康复护理评估记录"
    
    # 检查是否存在康复护理评估记录
    @classmethod
    def check_record_date_exists(cls,maternity_admission_id,record_date,exclude_id=None):
        if exclude_id:
            return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exclude(id=exclude_id).exists()
        return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exists()
    
    

# 产妇每日生理护理记录
class MaternityDailyPhysicalCareRecord(BaseModel):
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, related_name='maternity_daily_physical_care_records')
    # 记录日期
    record_date = models.DateField(verbose_name='记录日期')
    # 普通饮食
    normal_diet = models.CharField(verbose_name='普通饮食', max_length=200,blank=True,default="")
    # 特殊饮食
    special_diet = models.CharField(verbose_name='特殊饮食', max_length=200,blank=True,default="")
    # 睡眠（小时）
    sleep_hours = models.PositiveIntegerField(verbose_name='睡眠（小时）', blank=True,null=True)
    # 小便
    urination = models.CharField(verbose_name='小便', max_length=100,blank=True,default="")
    # 大便
    defecation = models.CharField(verbose_name='大便', max_length=100,blank=True,default="")
    # 刷牙
    brushing_teeth = models.CharField(verbose_name='刷牙', max_length=100,blank=True,default="")
    # 洗头
    wash_hair = models.CharField(verbose_name='洗头', max_length=100,blank=True,default="")
    # 沐浴
    bathe = models.CharField(verbose_name='沐浴', max_length=100,blank=True,default="")
    # 会阴清洗
    perineal_cleaning = models.CharField(verbose_name='会阴清洗', max_length=100,blank=True,default="")
    # 运动时间
    exercise_time = models.CharField(verbose_name='运动时间', max_length=100,blank=True,default="")
    # 运动后评估
    # 耐受性
    exercise_tolerance = models.CharField(verbose_name='耐受性', max_length=30,blank=True,choices=ExerciseAfterEvaluationEnum.choices,default=ExerciseAfterEvaluationEnum.UNKNOWN)
    # 理解程度
    exercise_understanding = models.CharField(verbose_name='理解程度', max_length=30,blank=True,choices=ExerciseAfterEvaluationEnum.choices,default=ExerciseAfterEvaluationEnum.UNKNOWN)
    # 生命体征
    # 心跳
    heart_rate = models.PositiveIntegerField(verbose_name='心跳', blank=True,null=True)
    # 血压
    blood_pressure = models.CharField(verbose_name='血压', max_length=100,blank=True,default="")
    # 运动方式
    exercise_method = models.CharField(verbose_name='运动方式', max_length=100,blank=True,default="")
    # 其他情况
    other_situation = models.TextField(verbose_name='其他情况', blank=True,default="")
    # 签名
    signature = models.CharField(verbose_name='签名',blank=True,max_length=30,default="")
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_maternity_daily_physical_care_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_maternity_daily_physical_care_records',verbose_name='创建人',blank=True,null=True)
    
    

    class Meta:
        verbose_name = "产妇每日生理护理记录"
        verbose_name_plural = "产妇每日生理护理记录"
        unique_together = [['maternity_admission', 'record_date']]
        
    
    def __str__(self):
        return f"{self.maternity_admission.maternity.name} ({self.record_date}) 的每日生理护理记录"
    
    # 检查是否存在每日生理护理记录
    @classmethod
    def check_record_date_exists(cls,maternity_admission_id,record_date,exclude_id=None):
        if exclude_id:
            return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exclude(id=exclude_id).exists()
        return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exists()
    
    
    
# 产妇膳食记录表
class MaternityDailyDietRecord(BaseModel):
    # 产妇入院记录
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, related_name='maternity_diet_records')
    # 记录日期
    record_date = models.DateField(verbose_name='记录日期')
    # 餐数
    meal_number = models.PositiveIntegerField(verbose_name='餐数', blank=True,null=True)
    # 进食情况
    eating_situation = models.CharField(verbose_name='进食情况', max_length=40,blank=True,choices=EatingSituationEnum.choices,default=EatingSituationEnum.UNKNOWN)
    # 普通饮食特点
    normal_diet_features = models.CharField(verbose_name='普通饮食特点', max_length=100,blank=True,default="")
    # 特殊饮食特点
    special_diet_features = models.CharField(verbose_name='特殊饮食特点', max_length=40,blank=True,choices=SpecialDietFeaturesEnum.choices,default=SpecialDietFeaturesEnum.UNKNOWN)
    # 碳水化合物
    carbohydrate = PositiveFloatField(verbose_name='碳水化合物', blank=True,null=True)
    # 蔬菜类
    vegetables_class = PositiveFloatField(verbose_name='蔬菜类', blank=True,null=True)
    # 水果类
    fruit_class = PositiveFloatField(verbose_name='水果类', blank=True,null=True)
    # 肉类
    meat_class = PositiveFloatField(verbose_name='肉类', blank=True,null=True)
    # 禽类
    poultry_class = PositiveFloatField(verbose_name='禽类', blank=True,null=True)
    # 鱼类
    fish_class = PositiveFloatField(verbose_name='鱼类', blank=True,null=True)
    # 蛋类
    egg_class = PositiveFloatField(verbose_name='蛋类', blank=True,null=True)
    # 豆制品类
    soy_products_class = PositiveFloatField(verbose_name='豆制品类', blank=True,null=True)
    # 牛奶
    milk = PositiveFloatField(verbose_name='牛奶', blank=True,null=True)
    # 奶制品类
    dairy_products_class = PositiveFloatField(verbose_name='奶制品类', blank=True,null=True)
    # 油类
    oil_class = PositiveFloatField(verbose_name='油类', blank=True,null=True)
    # 中药材
    chinese_medicines_class = PositiveFloatField(verbose_name='中药材', blank=True,null=True)
    # 编号
    record_id = models.CharField(max_length=50, verbose_name="记录编号",blank=True, default=generate_maternity_diet_record_code)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.CASCADE, related_name='creator_maternity_diet_records',verbose_name='创建人',blank=True,null=True)
    
    
    class Meta:
        verbose_name = "产妇膳食记录表"
        verbose_name_plural = "产妇膳食记录表"
        unique_together = [['maternity_admission', 'record_date']]
        
    
    def __str__(self):
            return f"{self.maternity_admission.maternity.name} ({self.record_date}) 的膳食记录表"
    
    # 检查是否存在膳食记录表
    @classmethod
    def check_record_date_exists(cls,maternity_admission_id,record_date,exclude_id=None):
        if exclude_id:
            return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exclude(id=exclude_id).exists()
        return cls.objects.filter(maternity_admission_id=maternity_admission_id,record_date=record_date).exists()
    
    
    
    
    